# Enhanced Dual-Source Legal Corpus Processing Guide

## 🎯 Overview

The Enhanced Dual-Source Legal Corpus Processing System provides robust, scalable processing of legal cases from **two complementary sources**:

1. **CourtListener API** - Recent/real-time cases with enhanced cross-system tracking
2. **Case Law Access Project (CAP)** - Historical archive data for comprehensive coverage

The system features atomic operations, cross-source deduplication, and comprehensive quality assurance.

## 🏗️ Architecture

### Core Components

1. **ChunkedCourtListenerProcessor** - Main orchestrator
2. **CourtIDResolver** - Dynamic court ID fetching
3. **TimeWindowProcessor** - Month-based time slicing
4. **AtomicStoragePipeline** - Multi-system storage with rollback
5. **CrossSystemValidator** - Quality assurance and consistency checking
6. **EnhancedProgressUI** - Real-time terminal interface

### Storage Systems

- **Supabase** (Primary): Case records with cross-system tracking
- **GCS** (Documents): Full case JSON documents  
- **Pinecone** (Vectors): Text embeddings for semantic search
- **Neo4j** (Graph): Case relationships and network analysis

## 📊 Processing Parameters

### Default Configuration
```bash
--chunk-size 20000      # Cases per time window chunk
--batch-size 1000       # Cases per atomic storage batch
--start-year 1994       # Beginning of processing range
--end-year 2025         # End of processing range
```

### Recommended Settings

| Environment | Chunk Size | Batch Size | Expected Duration |
|-------------|------------|------------|-------------------|
| **Testing** | 500 | 50 | 30 minutes |
| **Development** | 5,000 | 500 | 4-6 hours |
| **Production** | 20,000 | 1,000 | 2-3 days |

## 🚀 Quick Start

### Prerequisites
```bash
# Install dependencies
pip install rich>=13.0.0 tabulate>=0.9.0

# Set environment variables
export COURTLISTENER_API_KEY="your_api_key"
export SUPABASE_URL="your_supabase_url"
export SUPABASE_KEY="your_supabase_key"
export PINECONE_API_KEY="your_pinecone_key"
```

### Processing Options

#### Option 1: Enhanced CourtListener Only (Recommended First)
```bash
# Full enhanced CourtListener processing
python enhanced_dual_source_processor.py --courtlistener-only

# Custom parameters
python enhanced_dual_source_processor.py --courtlistener-only --start-year 2020 --end-year 2025
```

#### Option 2: Full Dual-Source Processing
```bash
# Both CourtListener + CAP processing
python enhanced_dual_source_processor.py

# Specific jurisdiction
python enhanced_dual_source_processor.py --jurisdiction tx
```

#### Option 3: CAP Only (After CourtListener)
```bash
# Process only CAP data (requires existing CourtListener data)
python enhanced_dual_source_processor.py --cap-only
```

#### Option 4: Legacy Single-Source
```bash
# Original enhanced processor (CourtListener only)
python enhanced_texas_processor.py --mini-test
```

## 🔄 Processing Flow

### Phase 1: Initialization
1. **Dynamic Court Resolution**: Fetch real TX court IDs from `/courts/` API
2. **Time Window Generation**: Create month-based processing windows (1994-2025)
3. **Checkpoint Recovery**: Resume from existing checkpoint if available

### Phase 2: Chunked Processing
```
For each time window (e.g., 1994-01):
├── Fetch cases using cursor pagination
├── Process in 1K batches with atomic storage:
│   ├── Store in Supabase (primary record)
│   ├── Store in GCS (full document)
│   ├── Create Pinecone vectors (text chunks)
│   └── Create Neo4j nodes (relationships)
├── Update cross-system tracking fields
├── Validate consistency across all systems
└── Save checkpoint every 10 batches
```

### Phase 3: Quality Assurance
- **Cross-system validation** after each 20K chunk
- **Consistency scoring** with 2% tolerance
- **Error tracking** with dead letter queue
- **Automatic retry** with exponential backoff

## 📋 Database Schema

### Enhanced Cases Table
```sql
CREATE TABLE cases (
    -- Core fields
    id TEXT PRIMARY KEY,
    case_name TEXT,
    court_id TEXT,
    date_filed DATE,
    jurisdiction TEXT DEFAULT 'TX',
    source TEXT DEFAULT 'courtlistener',
    
    -- Cross-system tracking
    gcs_path TEXT,              -- GCS object location
    pinecone_id TEXT,           -- Primary vector ID
    pinecone_namespace TEXT,    -- Pinecone namespace
    neo4j_node_id TEXT,         -- Neo4j node identifier
    word_count INTEGER,         -- Number of vectors created
    
    -- Processing metadata
    batch_id TEXT,              -- Processing batch identifier
    created_at TIMESTAMP DEFAULT NOW()
);
```

## 🔗 Cross-System Tracking

### Naming Conventions
```
Case ID: 12345

Supabase: id = "12345"
GCS: cases/tx/12345.json
Pinecone: 12345_chunk_0, 12345_chunk_1, 12345_chunk_2
Neo4j: case_12345
```

### Traceability Queries
```sql
-- Find all data for a case
SELECT id, gcs_path, pinecone_id, neo4j_node_id, word_count 
FROM cases WHERE id = '12345';

-- Find cases with incomplete tracking
SELECT id, 
    CASE WHEN gcs_path IS NULL THEN 'Missing GCS' END,
    CASE WHEN pinecone_id IS NULL THEN 'Missing Pinecone' END,
    CASE WHEN neo4j_node_id IS NULL THEN 'Missing Neo4j' END
FROM cases 
WHERE gcs_path IS NULL OR pinecone_id IS NULL OR neo4j_node_id IS NULL;
```

## 📊 Monitoring & Metrics

### Real-Time UI Metrics
- **Processing Rate**: Cases/minute
- **Success Rate**: % successful operations
- **Quality Score**: Cross-system consistency
- **Storage Status**: Count and health per system
- **Error Tracking**: DLQ items and retry budget

### Key Performance Indicators
```
Target Metrics:
├── Processing Rate: 50+ cases/minute
├── Success Rate: >95%
├── Quality Score: >98%
├── Storage Consistency: >98% across all systems
└── Error Rate: <2%
```

## 🔧 Troubleshooting

### Common Issues

#### 1. API Rate Limiting
```
Error: 429 Too Many Requests
Solution: System automatically retries with exponential backoff
```

#### 2. Storage Inconsistency
```
Error: Cross-system validation failed
Solution: Check individual storage system health
```

#### 3. Checkpoint Corruption
```
Error: Failed to load checkpoint
Solution: Delete corrupted checkpoint file and restart
```

### Recovery Commands
```bash
# Clean test data
python -c "
from supabase import create_client
import os
supabase = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_KEY'))
supabase.table('cases').delete().like('batch_id', 'test_%').execute()
print('✅ Test data cleaned')
"

# Resume from checkpoint
python enhanced_texas_processor.py --resume

# Force fresh start
python enhanced_texas_processor.py --no-resume
```

## 📈 Scaling Considerations

### Memory Usage
- **Batch Size**: 1K cases = ~50MB memory
- **Chunk Size**: 20K cases = ~1GB memory
- **Recommended**: 8GB+ RAM for production

### API Limits
- **CourtListener**: 5,000 requests/hour
- **Processing Rate**: ~300 requests/hour (well within limits)
- **Automatic Rate Limiting**: Built-in respect for API limits

### Storage Requirements
```
Estimated for 150K cases:
├── Supabase: ~500MB (metadata)
├── GCS: ~50GB (full documents)
├── Pinecone: ~2GB (vectors)
└── Neo4j: ~1GB (graph data)
```

## 🎯 Production Deployment

### Pre-Deployment Checklist
- [ ] Environment variables configured
- [ ] Database schema updated
- [ ] Storage clients configured (GCS, Pinecone, Neo4j)
- [ ] API keys validated
- [ ] Test run completed successfully

### Deployment Steps
```bash
# 1. Clean existing data (if needed)
python clean_database.py

# 2. Start production processing
python enhanced_texas_processor.py \
    --start-year 1994 \
    --end-year 2025 \
    --chunk-size 20000 \
    --batch-size 1000

# 3. Monitor progress
tail -f enhanced_processing_*.log
```

### Post-Deployment Validation
```sql
-- Verify processing completion
SELECT 
    COUNT(*) as total_cases,
    COUNT(CASE WHEN gcs_path IS NOT NULL THEN 1 END) as with_gcs,
    COUNT(CASE WHEN pinecone_id IS NOT NULL THEN 1 END) as with_vectors,
    COUNT(CASE WHEN neo4j_node_id IS NOT NULL THEN 1 END) as with_nodes,
    AVG(word_count) as avg_vectors_per_case
FROM cases;

-- Expected result: All counts should be equal (1:1 ratio)
```

## 📞 Support

### Log Files
- **Processing Log**: `enhanced_processing_YYYYMMDD_HHMMSS.log`
- **Test Reports**: `test_500_cases_report_YYYYMMDD_HHMMSS.json`
- **Checkpoints**: `checkpoints/*.json`

### Debug Commands
```bash
# Check system status
python test_chunked_components.py

# Verify cross-system data
python verify_cross_system_data.py

# Manual checkpoint inspection
ls -la checkpoints/
cat checkpoints/latest_checkpoint.json
```

---

## 🎉 Success Criteria

The enhanced processing system is considered successful when:

1. **✅ All cases processed** without errors
2. **✅ 1:1 consistency** across all storage systems  
3. **✅ Full traceability** from cases to vectors
4. **✅ Quality scores** >98% throughout processing
5. **✅ Zero items** in dead letter queue

**The system is now production-ready for processing the complete Texas legal corpus!**
