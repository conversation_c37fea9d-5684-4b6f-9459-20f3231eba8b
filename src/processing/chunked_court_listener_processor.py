"""
Chunked CourtListener Processor
Main orchestrator that integrates all enhanced components for robust processing
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, AsyncGenerator
from pathlib import Path
import httpx

from .court_id_resolver import CourtIDResolver
from .time_window_processor import TimeWindowProcessor, TimeWindow
from .atomic_storage_pipeline import AtomicStoragePipeline
from .atomic_checkpoint_manager import AtomicCheckpointManager
from .retry_manager import RetryManager
from .cross_system_validator import CrossSystemValidator
from ..ui.enhanced_progress_ui import EnhancedProgressUI

logger = logging.getLogger(__name__)


class ChunkedCourtListenerProcessor:
    """
    Main processor that orchestrates chunked processing with enhanced features:
    - Dynamic court ID resolution
    - Time-window based processing
    - Atomic storage with rollback
    - Robust checkpoint/resume
    - Rich terminal UI
    """
    
    def __init__(
        self,
        api_key: str,
        supabase_client,
        gcs_client,
        pinecone_client,
        neo4j_client,
        chunk_size: int = 20000,
        batch_size: int = 1000,
        base_url: str = "https://www.courtlistener.com/api/rest/v4"
    ):
        self.api_key = api_key
        self.base_url = base_url
        self.chunk_size = chunk_size
        self.batch_size = batch_size
        
        # Initialize HTTP session
        self.session = httpx.AsyncClient(
            headers={"Authorization": f"Token {api_key}"},
            timeout=30.0,
            limits=httpx.Limits(max_keepalive_connections=5, max_connections=10)
        )
        
        # Initialize components
        self.court_resolver = CourtIDResolver(api_key, base_url)
        self.time_processor = TimeWindowProcessor(self.session, base_url)
        self.storage_pipeline = AtomicStoragePipeline(
            supabase_client, gcs_client, pinecone_client, neo4j_client, batch_size
        )
        self.checkpoint_manager = AtomicCheckpointManager()
        self.retry_manager = RetryManager()
        self.validator = CrossSystemValidator(
            supabase_client, gcs_client, pinecone_client, neo4j_client
        )
        self.progress_ui = EnhancedProgressUI()
        
        # Processing state
        self.texas_court_ids = []
        self.processing_stats = {
            'start_time': None,
            'chunks_completed': 0,
            'total_chunks': 0,
            'cases_fetched': 0,
            'cases_processed': 0,
            'cases_stored': 0,
            'errors': 0
        }
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.progress_ui.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.progress_ui.stop()
        await self.session.aclose()
    
    async def process_jurisdiction_chunked(
        self,
        jurisdiction: str = 'tx',
        start_year: int = 1994,
        end_year: int = 2025,
        resume: bool = True
    ) -> Dict[str, Any]:
        """
        Process jurisdiction with chunked approach and enhanced features
        
        Args:
            jurisdiction: Jurisdiction code (e.g., 'tx')
            start_year: Starting year for processing
            end_year: Ending year for processing
            resume: Whether to resume from checkpoint
            
        Returns:
            Processing results summary
        """
        self.processing_stats['start_time'] = datetime.now()
        
        try:
            # Phase 1: Initialize
            await self._initialize_processing(jurisdiction, start_year, end_year, resume)
            
            # Phase 2: Process chunks
            results = await self._process_chunks(jurisdiction)
            
            # Phase 3: Finalize
            await self._finalize_processing(jurisdiction, results)
            
            return results
            
        except Exception as e:
            logger.error(f"Processing failed for {jurisdiction}: {e}")
            self.progress_ui.log_error(f"Processing failed: {e}")
            raise
    
    async def _initialize_processing(
        self,
        jurisdiction: str,
        start_year: int,
        end_year: int,
        resume: bool
    ):
        """Initialize processing components"""
        self.progress_ui.set_phase("initializing")
        
        # Step 1: Resolve Texas court IDs dynamically
        logger.info("🔍 Resolving Texas court IDs...")
        self.texas_court_ids = await self.court_resolver.fetch_texas_court_ids()
        logger.info(f"✅ Found {len(self.texas_court_ids)} Texas courts: {self.texas_court_ids}")
        
        # Step 2: Generate time windows
        logger.info(f"📅 Generating time windows ({start_year}-{end_year})...")
        self.time_windows = self.time_processor.generate_time_windows(start_year, end_year)
        logger.info(f"✅ Generated {len(self.time_windows)} time windows")
        
        # Step 3: Check for resumable checkpoint
        if resume:
            checkpoint = self.checkpoint_manager.find_resumable_checkpoint(jurisdiction, "fetch")
            if checkpoint:
                logger.info(f"📂 Found resumable checkpoint: {checkpoint.checkpoint_id}")
                await self._resume_from_checkpoint(checkpoint)
                return
        
        # Step 4: Initialize fresh processing
        self.processing_stats['total_chunks'] = len(self.time_windows)
        self.progress_ui.update_stats({
            'total_windows': len(self.time_windows),
            'windows_completed': 0
        })
    
    async def _process_chunks(self, jurisdiction: str) -> Dict[str, Any]:
        """Process all chunks with quality verification"""
        self.progress_ui.set_phase("processing")
        
        chunk_results = []
        
        for chunk_idx, window in enumerate(self.time_windows):
            try:
                # Process single chunk
                chunk_result = await self._process_single_chunk(
                    window, chunk_idx + 1, len(self.time_windows), jurisdiction
                )
                chunk_results.append(chunk_result)
                
                # Update progress
                self.processing_stats['chunks_completed'] += 1
                self.progress_ui.set_window(str(window), chunk_idx + 1, len(self.time_windows))
                
                # Quality verification after each chunk
                await self._verify_chunk_quality(chunk_result, jurisdiction)
                
            except Exception as e:
                logger.error(f"Chunk {chunk_idx + 1} failed: {e}")
                self.progress_ui.log_error(f"Chunk {chunk_idx + 1} failed: {e}")
                self.processing_stats['errors'] += 1
                
                # Continue with next chunk
                continue
        
        return {
            'chunks_processed': len(chunk_results),
            'chunks_failed': self.processing_stats['errors'],
            'total_cases_processed': sum(r.get('cases_stored', 0) for r in chunk_results),
            'processing_time': datetime.now() - self.processing_stats['start_time']
        }
    
    async def _process_single_chunk(
        self,
        window: TimeWindow,
        chunk_num: int,
        total_chunks: int,
        jurisdiction: str
    ) -> Dict[str, Any]:
        """Process a single time window chunk"""
        logger.info(f"📦 Processing chunk {chunk_num}/{total_chunks}: {window}")
        
        chunk_stats = {
            'window': window,
            'chunk_num': chunk_num,
            'cases_fetched': 0,
            'cases_stored': 0,
            'batches_processed': 0,
            'start_time': datetime.now()
        }
        
        # Fetch cases for this window
        cases_batch = []
        batch_count = 0
        
        async for api_response in self.time_processor.process_window(
            window, self.texas_court_ids, {'format': 'json', 'page_size': 100}
        ):
            cases = api_response.get('results', [])
            cases_batch.extend(cases)
            chunk_stats['cases_fetched'] += len(cases)
            
            # Process in batches of 1000
            while len(cases_batch) >= self.batch_size:
                batch = cases_batch[:self.batch_size]
                cases_batch = cases_batch[self.batch_size:]
                
                # Store batch atomically
                batch_result = await self.retry_manager.retry_with_backoff(
                    self.storage_pipeline.store_batch,
                    batch,
                    f"chunk_{chunk_num}_batch_{batch_count}",
                    item_id=f"chunk_{chunk_num}_batch_{batch_count}",
                    context={'window': str(window), 'chunk_num': chunk_num}
                )
                
                if batch_result.success:
                    chunk_stats['cases_stored'] += batch_result.total_items
                    chunk_stats['batches_processed'] += 1
                    batch_count += 1
                    
                    # Update progress
                    self.progress_ui.update_batch_progress(batch_count, chunk_stats['cases_fetched'] // self.batch_size)
                
                # Save checkpoint every 10 batches
                if batch_count % 10 == 0:
                    await self._save_chunk_checkpoint(chunk_stats, jurisdiction)
        
        # Process remaining cases
        if cases_batch:
            batch_result = await self.retry_manager.retry_with_backoff(
                self.storage_pipeline.store_batch,
                cases_batch,
                f"chunk_{chunk_num}_final",
                item_id=f"chunk_{chunk_num}_final",
                context={'window': str(window), 'chunk_num': chunk_num}
            )
            
            if batch_result.success:
                chunk_stats['cases_stored'] += batch_result.total_items
                chunk_stats['batches_processed'] += 1
        
        chunk_stats['end_time'] = datetime.now()
        chunk_stats['duration'] = chunk_stats['end_time'] - chunk_stats['start_time']
        
        logger.info(f"✅ Chunk {chunk_num} complete: {chunk_stats['cases_stored']} cases stored")
        return chunk_stats
    
    async def _verify_chunk_quality(self, chunk_result: Dict[str, Any], jurisdiction: str):
        """Verify quality after processing a chunk using cross-system validation"""
        logger.info(f"🔍 Verifying quality for chunk {chunk_result['chunk_num']}")

        # Use the cross-system validator for real validation
        batch_id_pattern = f"chunk_{chunk_result['chunk_num']}_"
        expected_count = chunk_result['cases_stored']

        # Run cross-system validation
        validation_report = await self.validator.validate_batch_consistency(
            batch_id=batch_id_pattern,
            expected_cases=expected_count,
            time_range={
                'start': chunk_result['window'].start,
                'end': chunk_result['window'].end
            }
        )

        # Update UI with real validation results
        for system, count in validation_report.actual_counts.to_dict().items():
            consistency = validation_report.consistency_scores[system]
            status = "✅" if consistency > 0.98 else "⚠️" if consistency > 0.95 else "❌"
            self.progress_ui.update_storage_status(system, count, consistency, status)

        # Update progress UI with validation results
        self.progress_ui.update_stats({
            'quality_score': validation_report.overall_score,
            'success_rate': chunk_result['cases_stored'] / max(chunk_result['cases_fetched'], 1)
        })

        # Log vector statistics if available
        if validation_report.vector_stats:
            avg_vectors = validation_report.vector_stats.get('avg_vectors_per_case', 0)
            total_vectors = validation_report.vector_stats.get('total_vectors', 0)
            logger.info(f"📊 Vector stats: {total_vectors} total vectors, {avg_vectors:.1f} avg per case")

        # Log any issues found
        if validation_report.issues:
            for issue in validation_report.issues:
                self.progress_ui.log_error(f"Consistency issue: {issue}")

        validation_status = "✅ PASSED" if validation_report.passed else "❌ FAILED"
        logger.info(f"✅ Quality verification complete: {validation_report.overall_score:.1%} overall quality ({validation_status})")

        return validation_report
    
    async def _save_chunk_checkpoint(self, chunk_stats: Dict[str, Any], jurisdiction: str):
        """Save checkpoint for current chunk"""
        checkpoint_id = f"chunked_{jurisdiction}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        checkpoint_data = {
            'checkpoint_id': checkpoint_id,
            'phase': 'process',
            'window': {
                'start': chunk_stats['window'].start,
                'end': chunk_stats['window'].end
            },
            'fetched': chunk_stats['cases_fetched'],
            'persisted': chunk_stats['cases_stored'],
            'court_ids': self.texas_court_ids,
            'metadata': {
                'jurisdiction': jurisdiction,
                'chunk_num': chunk_stats['chunk_num'],
                'batches_processed': chunk_stats['batches_processed']
            }
        }
        
        success = await self.checkpoint_manager.save_checkpoint(checkpoint_data)
        if success:
            self.progress_ui.save_checkpoint(checkpoint_id)
            logger.debug(f"💾 Checkpoint saved: {checkpoint_id}")
    
    async def _resume_from_checkpoint(self, checkpoint):
        """Resume processing from a checkpoint"""
        logger.info(f"🔄 Resuming from checkpoint: {checkpoint.checkpoint_id}")
        
        # Update processing stats from checkpoint
        self.processing_stats.update({
            'cases_fetched': checkpoint.fetched,
            'cases_processed': checkpoint.persisted,
            'chunks_completed': checkpoint.metadata.get('chunk_num', 0)
        })
        
        # Update UI
        self.progress_ui.update_stats({
            'cases_fetched': checkpoint.fetched,
            'cases_processed': checkpoint.persisted,
            'windows_completed': checkpoint.metadata.get('chunk_num', 0)
        })
        
        # Filter time windows to resume from correct position
        chunk_num = checkpoint.metadata.get('chunk_num', 0)
        if chunk_num > 0:
            self.time_windows = self.time_windows[chunk_num:]
            logger.info(f"📅 Resuming from chunk {chunk_num + 1}, {len(self.time_windows)} windows remaining")
    
    async def _finalize_processing(self, jurisdiction: str, results: Dict[str, Any]):
        """Finalize processing and cleanup"""
        self.progress_ui.set_phase("finalizing")
        
        # Save final checkpoint
        final_checkpoint_id = f"chunked_{jurisdiction}_complete_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        final_checkpoint_data = {
            'checkpoint_id': final_checkpoint_id,
            'phase': 'complete',
            'fetched': self.processing_stats['cases_fetched'],
            'persisted': self.processing_stats['cases_processed'],
            'court_ids': self.texas_court_ids,
            'metadata': {
                'jurisdiction': jurisdiction,
                'total_chunks': results['chunks_processed'],
                'processing_time': str(results['processing_time']),
                'final_stats': results
            }
        }
        
        await self.checkpoint_manager.save_checkpoint(final_checkpoint_data)
        self.progress_ui.save_checkpoint(final_checkpoint_id)
        
        # Final UI update
        self.progress_ui.set_phase("complete")
        
        logger.info(f"🎉 Processing complete for {jurisdiction}")
        logger.info(f"📊 Final stats: {results}")


# Convenience function
async def process_texas_corpus(
    api_key: str,
    supabase_client,
    gcs_client,
    pinecone_client,
    neo4j_client,
    **kwargs
) -> Dict[str, Any]:
    """Process the complete Texas legal corpus with enhanced features"""
    async with ChunkedCourtListenerProcessor(
        api_key, supabase_client, gcs_client, pinecone_client, neo4j_client, **kwargs
    ) as processor:
        return await processor.process_jurisdiction_chunked('tx')


if __name__ == "__main__":
    # Test the chunked processor
    import os
    from dotenv import load_dotenv
    
    load_dotenv()
    api_key = os.getenv("COURTLISTENER_API_KEY")
    
    if not api_key:
        print("Error: COURTLISTENER_API_KEY not found in environment")
        exit(1)
    
    async def test_chunked_processing():
        # Mock clients for testing
        class MockClient:
            def __init__(self, name):
                self.name = name
        
        async with ChunkedCourtListenerProcessor(
            api_key,
            MockClient('supabase'),
            MockClient('gcs'),
            MockClient('pinecone'),
            MockClient('neo4j'),
            chunk_size=1000  # Small chunk for testing
        ) as processor:
            # Test with a small date range
            results = await processor.process_jurisdiction_chunked(
                'tx', start_year=2024, end_year=2024
            )
            print(f"Test results: {results}")
    
    asyncio.run(test_chunked_processing())
