#!/usr/bin/env python3
"""
Neo4j Graph Sync Module
Automatically syncs processed opinions into the Neo4j graph database.
Ensures every new or updated opinion is reflected in the graph with proper relationships.
"""

import os
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from neo4j import GraphDatabase, Transaction
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class OpinionData:
    """Data structure for opinion information."""
    opinion_id: str
    court_id: str
    year: int
    practice_areas: List[str]
    citation: Optional[str] = None
    docket: Optional[str] = None
    gcs_uri: Optional[str] = None
    case_name: Optional[str] = None
    date_filed: Optional[str] = None
    source: Optional[str] = None
    source_window: Optional[str] = None

class Neo4jGraphSync:
    """Handles synchronization of opinions to Neo4j graph database."""
    
    def __init__(self, uri: str = None, username: str = None, password: str = None):
        """Initialize Neo4j connection."""
        self.uri = uri or os.getenv('NEO4J_URI', 'bolt://localhost:7687')
        self.username = username or os.getenv('NEO4J_USERNAME', 'neo4j')
        self.password = password or os.getenv('NEO4J_PASSWORD', 'password')
        
        self.driver = None
        self.batch_size = 1000
        self.pending_opinions = []
        self.pending_citations = []
        
        logger.info(f"Initialized Neo4j sync for {self.uri}")
    
    def connect(self):
        """Establish connection to Neo4j."""
        try:
            self.driver = GraphDatabase.driver(
                self.uri, 
                auth=(self.username, self.password)
            )
            # Test connection
            with self.driver.session() as session:
                session.run("RETURN 1")
            logger.info("✅ Neo4j connection established")
        except Exception as e:
            logger.error(f"❌ Failed to connect to Neo4j: {e}")
            raise
    
    def close(self):
        """Close Neo4j connection."""
        if self.driver:
            self.driver.close()
            logger.info("Neo4j connection closed")
    
    def write_opinion(self, opinion: OpinionData):
        """Add opinion to batch for writing."""
        self.pending_opinions.append(opinion)
        
        if len(self.pending_opinions) >= self.batch_size:
            self.flush_opinions()
    
    def write_citation(self, citing_opinion_id: str, cited_opinion_id: str):
        """Add citation to batch for writing."""
        self.pending_citations.append({
            'citing': citing_opinion_id,
            'cited': cited_opinion_id
        })
        
        if len(self.pending_citations) >= self.batch_size:
            self.flush_citations()
    
    def flush_opinions(self):
        """Write all pending opinions to Neo4j."""
        if not self.pending_opinions:
            return
        
        if not self.driver:
            self.connect()
        
        try:
            with self.driver.session() as session:
                session.execute_write(self._write_opinions_batch, self.pending_opinions)
            
            logger.info(f"✅ Synced {len(self.pending_opinions)} opinions to Neo4j")
            self.pending_opinions.clear()
            
        except Exception as e:
            logger.error(f"❌ Failed to sync opinions to Neo4j: {e}")
            raise
    
    def flush_citations(self):
        """Write all pending citations to Neo4j."""
        if not self.pending_citations:
            return
        
        if not self.driver:
            self.connect()
        
        try:
            with self.driver.session() as session:
                session.execute_write(self._write_citations_batch, self.pending_citations)
            
            logger.info(f"✅ Synced {len(self.pending_citations)} citations to Neo4j")
            self.pending_citations.clear()
            
        except Exception as e:
            logger.error(f"❌ Failed to sync citations to Neo4j: {e}")
            raise
    
    def _write_opinions_batch(self, tx: Transaction, opinions: List[OpinionData]):
        """Write batch of opinions in a single transaction."""
        
        # Upsert opinions with relationships
        query = """
        UNWIND $opinions AS op
        
        // Upsert Opinion node
        MERGE (o:Opinion {opinion_id: op.opinion_id})
        ON CREATE SET 
            o.court_id = op.court_id,
            o.year = op.year,
            o.practice_areas = op.practice_areas,
            o.citation = op.citation,
            o.docket = op.docket,
            o.gcs_uri = op.gcs_uri,
            o.case_name = op.case_name,
            o.date_filed = op.date_filed,
            o.source = op.source,
            o.source_window = op.source_window,
            o.created_at = datetime(),
            o.updated_at = datetime()
        ON MATCH SET
            o.court_id = op.court_id,
            o.year = op.year,
            o.practice_areas = op.practice_areas,
            o.citation = op.citation,
            o.docket = op.docket,
            o.gcs_uri = op.gcs_uri,
            o.case_name = op.case_name,
            o.date_filed = op.date_filed,
            o.source = op.source,
            o.source_window = op.source_window,
            o.updated_at = datetime()
        
        // Upsert Court and relationship
        MERGE (c:Court {court_id: op.court_id})
        MERGE (c)-[:HEARS_BEFORE]->(o)
        
        // Upsert Practice Area relationships
        WITH o, op
        UNWIND op.practice_areas AS pa_id
        MERGE (pa:PA {pa_id: pa_id})
        MERGE (o)-[:HAS_PA]->(pa)
        """
        
        # Convert opinions to dict format
        opinion_dicts = []
        for op in opinions:
            opinion_dicts.append({
                'opinion_id': op.opinion_id,
                'court_id': op.court_id,
                'year': op.year,
                'practice_areas': op.practice_areas,
                'citation': op.citation,
                'docket': op.docket,
                'gcs_uri': op.gcs_uri,
                'case_name': op.case_name,
                'date_filed': op.date_filed,
                'source': op.source,
                'source_window': op.source_window
            })
        
        tx.run(query, opinions=opinion_dicts)
    
    def _write_citations_batch(self, tx: Transaction, citations: List[Dict[str, str]]):
        """Write batch of citations in a single transaction."""
        
        query = """
        UNWIND $citations AS cit
        MATCH (citing:Opinion {opinion_id: cit.citing})
        MATCH (cited:Opinion {opinion_id: cit.cited})
        MERGE (citing)-[:CITES]->(cited)
        """
        
        tx.run(query, citations=citations)
    
    def flush_all(self):
        """Flush all pending operations."""
        self.flush_opinions()
        self.flush_citations()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get current graph statistics."""
        if not self.driver:
            self.connect()
        
        with self.driver.session() as session:
            # Node counts
            node_result = session.run("""
                MATCH (c:Court) WITH count(c) as courts
                MATCH (pa:PA) WITH courts, count(pa) as practice_areas
                MATCH (o:Opinion) WITH courts, practice_areas, count(o) as opinions
                RETURN courts, practice_areas, opinions
            """)
            node_counts = node_result.single()
            
            # Relationship counts
            rel_result = session.run("""
                MATCH ()-[h:HEARS_BEFORE]->() WITH count(h) as hears
                MATCH ()-[p:HAS_PA]->() WITH hears, count(p) as has_pa
                MATCH ()-[c:CITES]->() WITH hears, has_pa, count(c) as cites
                RETURN hears, has_pa, cites
            """)
            rel_counts = rel_result.single()
            
            return {
                'nodes': {
                    'courts': node_counts['courts'],
                    'practice_areas': node_counts['practice_areas'],
                    'opinions': node_counts['opinions'],
                    'total': node_counts['courts'] + node_counts['practice_areas'] + node_counts['opinions']
                },
                'relationships': {
                    'hears_before': rel_counts['hears'],
                    'has_pa': rel_counts['has_pa'],
                    'cites': rel_counts['cites'],
                    'total': rel_counts['hears'] + rel_counts['has_pa'] + rel_counts['cites']
                },
                'timestamp': datetime.now().isoformat()
            }

# Global instance for pipeline integration
_graph_sync = None

def get_graph_sync() -> Neo4jGraphSync:
    """Get or create global graph sync instance."""
    global _graph_sync
    if _graph_sync is None:
        _graph_sync = Neo4jGraphSync()
    return _graph_sync

def write_opinion(opinion_data: Dict[str, Any]):
    """Convenience function to write opinion to graph."""
    sync = get_graph_sync()
    
    # Convert dict to OpinionData
    opinion = OpinionData(
        opinion_id=opinion_data.get('id', opinion_data.get('opinion_id')),
        court_id=opinion_data.get('court_id'),
        year=opinion_data.get('year_filed', opinion_data.get('year')),
        practice_areas=opinion_data.get('practice_areas', []),
        citation=opinion_data.get('citation'),
        docket=opinion_data.get('docket_number', opinion_data.get('docket')),
        gcs_uri=opinion_data.get('gcs_uri'),
        case_name=opinion_data.get('case_name'),
        date_filed=opinion_data.get('date_filed'),
        source=opinion_data.get('source'),
        source_window=opinion_data.get('source_window')
    )
    
    sync.write_opinion(opinion)

def write_citation(citing_opinion_id: str, cited_opinion_id: str):
    """Convenience function to write citation to graph."""
    sync = get_graph_sync()
    sync.write_citation(citing_opinion_id, cited_opinion_id)

def flush_all():
    """Flush all pending graph operations."""
    sync = get_graph_sync()
    sync.flush_all()

def get_stats() -> Dict[str, Any]:
    """Get current graph statistics."""
    sync = get_graph_sync()
    return sync.get_stats()

def close_connection():
    """Close graph connection."""
    global _graph_sync
    if _graph_sync:
        _graph_sync.close()
        _graph_sync = None
