#!/usr/bin/env python3
"""
Test Enhanced CAP Judge Extraction
Tests if our enhanced patterns can extract full names from later CAP data (1980s-1993)
"""

import re
from judge_relationship_enhancer import JudgeRelationshipEnhancer

def test_cap_judge_patterns():
    """Test judge extraction patterns on real CAP data formats"""
    
    print("🧪 TESTING ENHANCED CAP JUDGE EXTRACTION")
    print("=" * 60)
    
    # Test cases from real CAP data
    test_cases = [
        {
            'name': 'Early CAP (1931)',
            'text': 'HICKMAN, C. J.\n\nMrs. <PERSON><PERSON>...',
            'expected': ['Hickman']
        },
        {
            'name': 'Later CAP (1984) - Full Name',
            'text': 'United States District Court, N.D. New York.\nMINER, District Judge.\n\nThis case...',
            'expected': ['Miner']  # Current behavior
        },
        {
            'name': 'Later CAP (1984) - Full Name with First',
            'text': 'United States District Court, M.D. Tennessee.\nJOHN <PERSON>, District Judge.\n\nThis matter...',
            'expected': ['<PERSON>']  # What we want
        },
        {
            'name': 'Later CAP (1980s) - Multiple Names',
            'text': 'Before WILLIAM H. REHNQUIST, SANDRA DAY O\'CONNOR, and ANTONIN SCALIA, Circuit Judges.\n\nThis appeal...',
            'expected': ['William H. Rehnquist', 'Sandra Day O\'Connor', 'Antonin Scalia']
        }
    ]
    
    enhancer = JudgeRelationshipEnhancer()
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 TEST {i}: {test_case['name']}")
        print(f"   Text: {test_case['text'][:80]}...")
        
        # Extract judges using our enhanced patterns
        judges = enhancer._extract_judges_from_text(test_case['text'])
        extracted_names = [judge['name'] for judge in judges]
        
        print(f"   Expected: {test_case['expected']}")
        print(f"   Extracted: {extracted_names}")
        
        # Check if we got the expected results
        success = set(extracted_names) == set(test_case['expected'])
        print(f"   Result: {'✅ PASS' if success else '❌ FAIL'}")
        
        if not success:
            print(f"   Issue: Missing {set(test_case['expected']) - set(extracted_names)} or Extra {set(extracted_names) - set(test_case['expected'])}")

def test_pattern_matching():
    """Test individual patterns against CAP formats"""
    
    print(f"\n🔍 TESTING INDIVIDUAL PATTERNS")
    print("=" * 60)
    
    # Test the new patterns we added
    patterns_to_test = [
        (r'([A-Z][A-Z\s\.]+[A-Z][A-Z]*),\s+District\s+Judge', 'JOHN T. NIXON, District Judge'),
        (r'([A-Z][A-Z\s\.]+[A-Z][A-Z]*),\s+Circuit\s+Judge', 'WILLIAM H. REHNQUIST, Circuit Judge'),
        (r'([A-Z][A-Z]+),\s+District\s+Judge', 'MINER, District Judge'),
        (r'([A-Z][A-Z]+),\s+C\.?\s*J\.?', 'HICKMAN, C. J.'),
    ]
    
    for pattern, test_text in patterns_to_test:
        print(f"\n📋 Pattern: {pattern}")
        print(f"   Test text: {test_text}")
        
        match = re.search(pattern, test_text)
        if match:
            extracted = match.group(1)
            print(f"   Extracted: '{extracted}' ✅")
        else:
            print(f"   No match ❌")

if __name__ == "__main__":
    test_cap_judge_patterns()
    test_pattern_matching()
